import { useAppContext } from "~/lib/providers/app-context";

export function NotificationPrompt() {
  const {
    notificationPermission,
    isNotificationSupported,
    requestNotificationPermission,
  } = useAppContext();

  // Don't show if notifications aren't supported
  if (!isNotificationSupported) {
    return null;
  }

  // Don't show if permission already granted or denied
  if (notificationPermission === "granted" || notificationPermission === "denied") {
    return null;
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg
            className="h-6 w-6 text-blue-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 17h5l-5 5v-5zM9 7h5l-5-5v5zM12 2v20"
            />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-blue-800">
            Stay updated with notifications
          </h3>
          <p className="mt-1 text-sm text-blue-700">
            Get notified about new messages, updates, and important announcements.
          </p>
          <div className="mt-3 flex space-x-3">
            <button
              onClick={requestNotificationPermission}
              className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Enable Notifications
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
