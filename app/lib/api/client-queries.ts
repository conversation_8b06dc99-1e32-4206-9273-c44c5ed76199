import { useQuery, useMutation } from "@tanstack/react-query";
import { HOSTS_CONFIG } from "~/config/hosts";

import type {
  GroupResponse,
  GroupsResponse,
  GroupCohortsResponse,
  CohortResponse,
  CourseResponse,
  LessonResponse,
  MyGroupsResponse,
  LiveClassesResponse,
  ProfileResponse,
  APIResponse,
  UserProfile,
  APISuccessResponse,
  ProfileByIdResponse,
  LiveClassResponse,
  NotificationsResponse,
  ModuleResponse,
} from "~/lib/api/types";
import { HttpError } from "~/lib/api/types";
import { queryClient } from "~/lib/providers/query-client";

const API_URL = HOSTS_CONFIG.api;

function useProfile() {
  return useQuery<APISuccessResponse<UserProfile>>({
    queryKey: ["profile"],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/profile`);
      const data = (await response.json()) as APIResponse<UserProfile>;
      if (!data.success) {
        throw new HttpError(data.data.message, response.status);
      }
      return data;
    },
  });
}

function useEditProfile() {
  return useMutation({
    mutationFn: async ({
      firstName,
      lastName,
      age,
      avatarURL,
      bio,
      gender,
      interestedTopics,
      phoneNumber,
    }: {
      firstName: string;
      lastName: string;
      age?: number;
      avatarURL?: string;
      bio?: string;
      gender: string | null;
      interestedTopics?: string[];
      phoneNumber?: string;
    }) => {
      const response = await fetch(`${API_URL}/profile`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          firstName,
          lastName,
          age,
          avatarURL,
          bio,
          gender,
          interestedTopics,
          phoneNumber,
        }),
      });
      const data = await response.json();
      if (!data.success) {
        throw new HttpError(data.data.message, response.status);
      }
      return data;
    },
    onMutate: async (newProfile) => {
      // Cancel any outgoing refetches
      // (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: ["profile"] });

      // Snapshot the previous value
      const previousProfile = queryClient.getQueryData<ProfileResponse>([
        "profile",
      ]);

      // Optimistically update to the new value
      queryClient.setQueryData(["profile"], (old: ProfileResponse) => ({
        ...old,
        data: {
          ...old.data,
          ...newProfile,
        },
      }));

      // Return a context object with the snapshotted value
      return { previousProfile };
    },
    onError: (err, newProfile, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousProfile) {
        queryClient.setQueryData(["profile"], context.previousProfile);
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: ["profile"],
      });
    },
  });
}

function useGroups() {
  return useQuery<GroupsResponse>({
    queryKey: ["groups"],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/groups`);
      const data = await response.json();
      return data;
    },
  });
}

function useMyGroups() {
  return useQuery<{
    success: boolean;
    groups: {
      byId: { [id: string]: MyGroupsResponse["data"]["groups"][0] };
      order: string[];
    };
  }>({
    queryKey: ["my", "groups"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_URL}/me/groups`);
        const data = (await response.json()) as MyGroupsResponse;

        if (data.success) {
          // Normalization
          let myGroups: {
            byId: {
              [externalId: string]: MyGroupsResponse["data"]["groups"][0];
            };
            order: string[];
          } = {
            byId: {},
            order: [],
          };
          data.data.groups.forEach((group) => {
            myGroups.byId[group.externalId] = group;
            myGroups.order.push(group.externalId);
          });

          return { success: true, groups: myGroups };
        }

        return { success: false, groups: { byId: {}, order: [] } };
      } catch (e) {
        throw e;
      }
    },
  });
}

function useGroup(id: string | number | null) {
  return useQuery<GroupResponse>({
    queryKey: ["groups", id],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/groups/${id}`);
      const data = await response.json();
      return data;
    },
    enabled: !!id,
  });
}

function useGroupCohorts(groupId: string | number) {
  return useQuery<GroupCohortsResponse>({
    queryKey: ["groups", groupId, "cohorts"],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/groups/${groupId}/cohorts`);
      const data = await response.json();
      return data;
    },
  });
}

function useCohort(
  groupId: string | number | null,
  cohortId: string | number | null
) {
  return useQuery<CohortResponse>({
    queryKey: ["groups", groupId, "cohorts", cohortId],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}`
      );
      const data = await response.json();
      return data;
    },
    enabled: !!groupId && !!cohortId, // Only run the query when both groupId and cohortId are set
  });
}

function useCourse(id: string | number) {
  return useQuery<CourseResponse>({
    queryKey: ["courses", id],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/courses/${id}`);
      const data = await response.json();
      return data;
    },
  });
}

function useLesson(
  courseId: string | number,
  sectionId: string | number,
  lessonId: string | number
) {
  return useQuery<LessonResponse>({
    queryKey: ["courses", courseId, "sections", sectionId, "lessons", lessonId],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}`
      );
      const data = await response.json();
      return data;
    },
  });
}

function useLiveClasses(
  groupId: string | number,
  cohortId: string | number,
  moduleId: string | number,
  isPresent: boolean
) {
  return useQuery<LiveClassesResponse>({
    queryKey: [
      "groups",
      groupId,
      "cohorts",
      cohortId,
      "modules",
      moduleId,
      "live-classes",
      "isPresent",
      isPresent,
    ],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}/live-classes?isPresent=${isPresent}`
      );
      const data = await response.json();
      return data;
    },
  });
}

function useLiveClass(
  groupId: string | number,
  cohortId: string | number,
  moduleId: string | number,
  liveClassId: string | number
) {
  return useQuery<LiveClassResponse>({
    queryKey: [
      "groups",
      groupId,
      "cohorts",
      cohortId,
      "modules",
      moduleId,
      "live-classes",
      liveClassId,
    ],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}/live-classes/${liveClassId}`
      );
      const data = await response.json();
      return data;
    },
  });
}

function useMyCohort(externalCohortId: string) {
  return useQuery<CohortResponse>({
    queryKey: ["me", "cohorts", externalCohortId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/me/cohorts/${externalCohortId}`);
      const data = await response.json();
      return data;
    },
  });
}

function useMyCohortModule(
  externalGroupId: string,
  externalCohortId: string,
  moduleId: string
) {
  return useQuery<ModuleResponse>({
    queryKey: ["me", "cohorts", externalCohortId, "modules", moduleId],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/groups/${externalGroupId}/cohorts/${externalCohortId}/modules/${moduleId}`
      );
      const data = await response.json();
      return data;
    },
  });
}

function useJoinCohort() {
  return useMutation({
    mutationFn: async ({
      groupId,
      cohortId,
    }: {
      groupId: string;
      cohortId: string;
    }) => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}/join`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = await response.json();
      return data;
    },
  });
}

function useGenerateGetStreamToken() {
  return useMutation({
    mutationFn: async () => {
      const response = await fetch(`${API_URL}/getstream/token/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
      const data = await response.json();

      return data;
    },
  });
}

function useGenerateGetStreamFeedToken() {
  return useMutation({
    mutationFn: async ({ feedId }: { feedId: string }) => {
      const response = await fetch(`${API_URL}/getstream/feed/token/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ feedId }),
      });
      const data = await response.json();
      return data;
    },
  });
}

function useCompleteLesson() {
  return useMutation({
    mutationFn: async ({
      courseId,
      sectionId,
      lessonId,
    }: {
      courseId: string;
      sectionId: string;
      lessonId: string;
    }) => {
      const response = await fetch(
        `${API_URL}/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}/complete`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = await response.json();
      return { courseId, sectionId, lessonId };
    },
    // onMutate: async (data) => {
    //   // Cancel any outgoing refetches
    //   // (so they don't overwrite our optimistic update)
    //   await queryClient.cancelQueries({ queryKey: ["courses", data?.courseId] });

    //   // Snapshot the previous value
    //   const previousCourse = queryClient.getQueryData([
    //     "courses",
    //     data?.courseId,
    //   ]);

    //   // Optimistically update to the new value
    //   queryClient.setQueryData(["courses", data?.courseId], (old: CourseResponse) => {
    //     if (!old?.data) return old;

    //     return {
    //       ...old,
    //       data: {
    //         ...old.data,
    //         sections: old.data.sections.map(section => {
    //           if (section.id === data?.sectionId) {
    //             return {
    //               ...section,
    //               lessons: section.lessons.map(lesson => {
    //                 if (lesson.id === data?.lessonId) {
    //                   return {
    //                     ...lesson,
    //                     isCompleted: true,
    //                   };
    //                 }
    //                 return lesson;
    //               })
    //             };
    //           }
    //           return section;
    //         })
    //       }
    //     };
    //   });

    //   // Return a context object with the snapshotted value
    //   return { previousCourse };
    // },
    onSuccess: (data) => {
      if (!data?.courseId || !data?.sectionId || !data?.lessonId) return;

      // Invalidate both the lesson and course queries
      queryClient.invalidateQueries({
        queryKey: [
          "courses",
          data?.courseId,
          "sections",
          data?.sectionId,
          "lessons",
          data?.lessonId,
        ],
      });

      queryClient.invalidateQueries({
        queryKey: ["courses", data?.courseId],
      });
    },
  });
}

function useRegisterPushNotificationToken() {
  return useMutation({
    mutationFn: async ({
      platform,
      token,
    }: {
      platform: string;
      token: string;
    }) => {
      const response = await fetch(`${API_URL}/notifications/device-tokens`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token, platform }),
      });
      const data = await response.json();
      console.log("Register push notification token response:", data);
      return data;
    },
  });
}

function useProfileById(supertokensUserId: string) {
  return useQuery<APISuccessResponse<ProfileByIdResponse>>({
    queryKey: ["profile", supertokensUserId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/profile/${supertokensUserId}`);
      const data = (await response.json()) as APIResponse<ProfileByIdResponse>;
      if (!data.success) {
        throw new HttpError(data.data.message, response.status);
      }
      return data;
    },
    enabled: !!supertokensUserId,
  });
}

function useRegisterLiveClass() {
  return useMutation({
    mutationFn: async ({
      groupId,
      cohortId,
      moduleId,
      liveClassId,
    }: {
      groupId: string;
      cohortId: string;
      moduleId: string;
      liveClassId: string;
    }) => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}/live-classes/${liveClassId}/register`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = (await response.json()) as APIResponse<null>;
      return { ...data, groupId, cohortId, moduleId, liveClassId };
    },
    onSuccess: ({ success, groupId, cohortId, moduleId, liveClassId }) => {
      if (!success) return;

      // Invalidate both the lesson and course queries
      queryClient.invalidateQueries({
        queryKey: [
          "groups",
          groupId,
          "cohorts",
          cohortId,
          "modules",
          moduleId,
          "live-classes",
          liveClassId,
        ],
      });

      // invalidate the isPresent query because the user only able to
      // register to a live class that is not over yet
      queryClient.invalidateQueries({
        queryKey: [
          "groups",
          groupId,
          "cohorts",
          cohortId,
          "modules",
          moduleId,
          "live-classes",
          "isPresent",
          true,
        ],
      });
    },
  });
}

function useNotifications() {
  return useQuery<NotificationsResponse>({
    queryKey: ["notifications"],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/me/notifications`);
      const data = await response.json();
      return data;
    },
  });
}

function useReadNotification() {
  return useMutation({
    mutationFn: async ({ notificationId }: { notificationId: string }) => {
      const response = await fetch(
        `${API_URL}/notifications/${notificationId}/read`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = (await response.json()) as APIResponse<null>;
      return { ...data, notificationId };
    },
    onMutate: ({ notificationId }) => {},
    onSuccess: ({ success, notificationId }) => {
      if (!success) return;

      queryClient.invalidateQueries({
        queryKey: ["notifications"],
      });
    },
  });
}

function useGetUploadUrl() {
  return useMutation({
    mutationFn: async ({
      contentType,
      fileName,
    }: {
      contentType: string;
      fileName: string;
    }) => {
      const response = await fetch(`${API_URL}/internal/storage/upload`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contentType,
          fileName,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new HttpError(
          result.message || "Failed to get upload URL",
          response.status
        );
      }

      // Return the nested data object
      return result.data as { cdnUrl: string; uploadUrl: string };
    },
  });
}

function useUploadAvatar() {
  return useMutation({
    mutationFn: async ({
      uploadUrl,
      uri,
      fileType,
    }: {
      uploadUrl: string;
      uri: string;
      fileType: string;
    }) => {
      try {
        // Try two different approaches for uploading

        // Approach 1: FormData (if the API expects multipart/form-data)
        try {
          const formData = new FormData();
          formData.append("file", {
            uri,
            type: fileType,
            name: `avatar.${fileType.split("/")[1]}`,
          } as any);

          const uploadResponse = await fetch(uploadUrl, {
            method: "PUT",
            body: formData,
          });

          if (uploadResponse.ok) {
            return uploadResponse;
          }
        } catch (formDataError) {
          // FormData approach failed, try raw binary
        }

        // Approach 2: Raw binary data (common for S3 presigned URLs)
        const response = await fetch(uri);
        if (!response.ok) {
          throw new HttpError("Failed to read image file", response.status);
        }

        const blob = await response.blob();

        const uploadResponse = await fetch(uploadUrl, {
          method: "PUT",
          headers: {
            "Content-Type": fileType,
          },
          body: blob,
        });

        if (!uploadResponse.ok) {
          const errorText = await uploadResponse
            .text()
            .catch(() => "No error text available");
          throw new HttpError(
            `Failed to upload avatar: ${uploadResponse.status} ${uploadResponse.statusText}`,
            uploadResponse.status
          );
        }

        return uploadResponse;
      } catch (error) {
        throw error;
      }
    },
  });
}

export {
  useProfile,
  useEditProfile,
  useGroups,
  useMyGroups,
  useGroup,
  useGroupCohorts,
  useCourse,
  useCohort,
  useLesson,
  useLiveClasses,
  useLiveClass,
  useMyCohort,
  useMyCohortModule,
  useJoinCohort,
  useGenerateGetStreamToken,
  useGenerateGetStreamFeedToken,
  useCompleteLesson,
  useRegisterPushNotificationToken,
  useProfileById,
  useRegisterLiveClass,
  useNotifications,
  useReadNotification,
  useGetUploadUrl,
  useUploadAvatar,
};
