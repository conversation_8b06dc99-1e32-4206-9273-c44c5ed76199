import {
  create<PERSON>ontext,
  use<PERSON>ontext,
  useEffect,
  useState,
  type <PERSON>actNode,
} from "react";
import Session from "supertokens-web-js/recipe/session";
import type { StreamChat, Channel, Thread } from "stream-chat";
import { connect } from "getstream";

import {
  useGenerateGetStreamToken,
  useRegisterPushNotificationToken,
} from "~/lib/api/client-queries";
import { queryClient } from "~/lib/providers/query-client";
const { messaging } = await import("~/config/firebase");

const GROUP_KEY = "current_group_id";
const COHORT_KEY = "current_cohort_id";
const FCM_TOKEN_KEY = "fcm_token";
const STREAM_API_KEY = import.meta.env.VITE_STREAM_API_KEY as
  | string
  | undefined;
const VAPID_KEY = import.meta.env.VITE_FIREBASE_VAPID_KEY as string | undefined;

if (!STREAM_API_KEY) {
  // eslint-disable-next-line no-console
  console.warn(
    "VITE_STREAM_API_KEY is not defined – Stream chat will be disabled."
  );
}

if (!VAPID_KEY) {
  // eslint-disable-next-line no-console
  console.warn(
    "VITE_FIREBASE_VAPID_KEY is not defined – Push notifications will be disabled."
  );
}

export type AppContextType = {
  chatClient: StreamChat | null;
  streamClient: any | null;
  userId: string | null;
  groupId: string | null;
  cohortId: string | null;
  streamToken: string | null;
  thread: Thread | null;
  channel: Channel | null;
  // Push notification related
  notificationPermission: NotificationPermission | null;
  fcmToken: string | null;
  isNotificationSupported: boolean;
  setUserId: (id: string | null) => void;
  setGroupId: (id: string | null) => void;
  setCohortId: (id: string | null) => void;
  setThread: (thread: Thread | null) => void;
  setChannel: (channel: Channel | null) => void;
  requestNotificationPermission: () => Promise<void>;
  cleanup: () => void;
  refreshAuth: () => Promise<void>;
};

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: ReactNode }) {
  const [userId, setUserId] = useState<string | null>(null);
  const [groupId, setGroupId] = useState<string | null>(null);
  const [cohortId, setCohortId] = useState<string | null>(null);
  const [thread, setThread] = useState<Thread | null>(null);
  const [channel, setChannel] = useState<Channel | null>(null);
  const [chatClient, setChatClient] = useState<StreamChat | null>(null);
  const [streamClient, setStreamClient] = useState<any | null>(null);

  // Push notification state
  const [notificationPermission, setNotificationPermission] =
    useState<NotificationPermission | null>(null);
  const [fcmToken, setFcmToken] = useState<string | null>(null);
  const [isNotificationSupported] = useState<boolean>(
    typeof window !== "undefined" &&
      "Notification" in window &&
      "serviceWorker" in navigator
  );

  const { mutate: generateToken, data: tokenData } =
    useGenerateGetStreamToken();
  const { mutate: registerPushToken } = useRegisterPushNotificationToken();

  // Convert API response shape → token string or null
  const streamToken: string | null = tokenData?.data?.token ?? null;

  // Function to get FCM token without prompting (call after permission granted)
  const getFcmToken = async () => {
    console.log("isNotificationSupported", isNotificationSupported);
    console.log("VAPID_KEY", VAPID_KEY);
    console.log("notificationPermission", notificationPermission);
    if (
      !isNotificationSupported ||
      !VAPID_KEY ||
      notificationPermission !== "granted"
    ) {
      console.warn(
        "Cannot get FCM token: notifications not supported, VAPID key missing, or permission not granted"
      );
      return;
    }

    try {
      const { getToken } = await import("firebase/messaging");

      if (!messaging) {
        console.error("Firebase messaging not available");
        return;
      }

      // Register service worker for background notifications
      const registration = await navigator.serviceWorker.register(
        "/firebase-messaging-sw.js"
      );
      console.log("Service worker registered:", registration);

      const token = await getToken(messaging, {
        vapidKey: VAPID_KEY,
        serviceWorkerRegistration: registration,
      });

      if (token) {
        setFcmToken(token);
        console.log("FCM Token generated:", token);

        // Register token with backend
        registerPushToken({
          platform: "web",
          token: token,
        });
      } else {
        console.log("No registration token available.");
      }
    } catch (error) {
      console.error("Error getting FCM token:", error);
    }
  };

  // Function to request notification permission (must be called from user gesture)
  const requestNotificationPermission = async () => {
    if (!isNotificationSupported || !VAPID_KEY) {
      console.warn(
        "Push notifications are not supported or VAPID key is missing"
      );
      return;
    }

    try {
      const permission = await Notification.requestPermission();
      console.log("Notification permission:", permission);
      setNotificationPermission(permission);
      // Note: Don't call getFcmToken() here - let the useEffect handle it
    } catch (error) {
      console.error("Error requesting notification permission:", error);
    }
  };

  // Check initial notification permission on mount
  useEffect(() => {
    if (isNotificationSupported) {
      setNotificationPermission(Notification.permission);
    }
  }, [isNotificationSupported]);

  // Handle notification permission changes
  useEffect(() => {
    console.log(
      "🔔 Notification permission changed to:",
      notificationPermission
    );

    if (notificationPermission === "granted") {
      console.log("✅ Permission granted - will get FCM token");
      // The existing useEffect below will handle getting the FCM token
    } else if (notificationPermission === "denied") {
      console.log("❌ Notification permission denied");
    } else if (notificationPermission === "default") {
      console.log("⚪ Notification permission is default (not yet requested)");
    }
  }, [notificationPermission]);

  // Fetch auth status & user id via SuperTokens once on mount
  useEffect(() => {
    let mounted = true;

    Session.doesSessionExist()
      .then((exist) => {
        if (!exist || !mounted) return;
        return Session.getUserId();
      })
      .then((id) => {
        if (id && mounted) {
          setUserId(id);
          // trigger token generation
          generateToken();
        }
      })
      .catch(() => {
        // Failed to initialize auth
      });

    return () => {
      mounted = false;
    };
  }, []);

  // Get FCM token when user is authenticated and permission is granted
  useEffect(() => {
    if (
      userId &&
      isNotificationSupported &&
      notificationPermission === "granted"
    ) {
      // User is authenticated and has permission - get fresh token
      console.log(
        "🔄 User authenticated with permission, getting fresh FCM token..."
      );
      getFcmToken();
    }
  }, [userId, isNotificationSupported, notificationPermission]);

  // Set up foreground message listener when FCM token is available
  useEffect(() => {
    if (!fcmToken || !isNotificationSupported) return;

    let unsubscribe: (() => void) | null = null;

    const setupMessageListener = async () => {
      try {
        const { onMessage } = await import("firebase/messaging");

        if (!messaging) {
          console.error(
            "Firebase messaging not available for message listener"
          );
          return;
        }

        console.log("🚀 Setting up foreground message listener");

        // Listen for foreground messages
        unsubscribe = onMessage(messaging, (payload) => {
          console.log("🔔 Notification received in foreground:", payload);
          console.log("🔔 Notification permission:", Notification.permission);
          console.log("🔔 Document visibility:", document.visibilityState);

          // Display native browser notification
          if (payload.notification?.title) {
            console.log(
              "✅ Creating notification with title:",
              payload.notification.title
            );

            try {
              const notification = new Notification(
                payload.notification.title,
                {
                  body: payload.notification.body || "",
                  icon: "/favicon.ico",
                  data: payload.data,
                  requireInteraction: false,
                  silent: false,
                }
              );

              console.log("✅ Notification created successfully");

              // Handle notification click
              notification.onclick = function (event) {
                console.log("🔔 Notification clicked");
                event.preventDefault();
                window.focus();

                // Navigate based on notification data
                if (payload.data?.url) {
                  window.location.href = payload.data.url;
                } else if (payload.data?.groupId) {
                  window.location.href = `/groups/${payload.data.groupId}`;
                } else if (payload.data?.type === "chat") {
                  window.location.href = "/chat";
                }

                notification.close();
              };

              // Auto-close after 5 seconds
              setTimeout(() => {
                notification.close();
              }, 5000);
            } catch (error) {
              console.error("❌ Failed to create notification:", error);
            }
          } else {
            console.log("❌ No notification title found");
          }
        });

        console.log("✅ Foreground message listener set up successfully");
      } catch (error) {
        console.error("❌ Error setting up message listener:", error);
      }
    };

    setupMessageListener();

    // Cleanup listener on unmount
    return () => {
      if (unsubscribe) {
        unsubscribe();
        console.log("🧹 Message listener cleaned up");
      }
    };
  }, [fcmToken, isNotificationSupported, messaging]);

  // Load persisted IDs
  useEffect(() => {
    const storedGroupId = localStorage.getItem(GROUP_KEY);
    if (storedGroupId) setGroupId(storedGroupId);
    const storedCohortId = localStorage.getItem(COHORT_KEY);
    if (storedCohortId) setCohortId(storedCohortId);
  }, []);

  // Dynamically create StreamChat client in the browser once we have auth
  useEffect(() => {
    if (!userId || !streamToken || !STREAM_API_KEY) return;

    let client: StreamChat;

    (async () => {
      const { StreamChat } = await import("stream-chat");
      client = StreamChat.getInstance(STREAM_API_KEY);
      await client.connectUser({ id: userId }, streamToken);
      setChatClient(client);
    })();

    return () => {
      if (client) {
        client.disconnectUser().catch(() => {});
      }
    };
    // Only when tokens or user change
  }, [userId, streamToken]);

  // Initialize GetStream client for feeds and notifications
  useEffect(() => {
    if (!userId || !streamToken || !STREAM_API_KEY) return;

    try {
      const client = connect(
        STREAM_API_KEY,
        streamToken,
        import.meta.env.VITE_STREAM_APP_ID
      );
      setStreamClient(client);
    } catch (error) {
      console.error("Failed to initialize GetStream client:", error);
    }
  }, [userId, streamToken]);

  const setGroupIdWithStorage = (id: string | null) => {
    if (id) {
      localStorage.setItem(GROUP_KEY, id);
    } else {
      localStorage.removeItem(GROUP_KEY);
    }
    setGroupId(id);
  };

  const setCohortIdWithStorage = (id: string | null) => {
    if (id) {
      localStorage.setItem(COHORT_KEY, id);
    } else {
      localStorage.removeItem(COHORT_KEY);
    }
    setCohortId(id);
  };

  const cleanup = () => {
    localStorage.removeItem(GROUP_KEY);
    localStorage.removeItem(COHORT_KEY);
    setGroupId(null);
    setCohortId(null);
    setUserId(null);
    setFcmToken(null);
    setNotificationPermission(null);
    queryClient.removeQueries();
  };

  const refreshAuth = async () => {
    try {
      const exists = await Session.doesSessionExist();
      if (exists) {
        const id = await Session.getUserId();
        if (id) {
          setUserId(id);
          generateToken();
        }
      }
    } catch (err) {
      console.error("Failed to refresh auth:", err);
    }
  };

  return (
    <AppContext.Provider
      value={{
        chatClient,
        streamClient,
        userId,
        groupId,
        cohortId,
        thread,
        channel,
        streamToken,
        // Push notification properties
        notificationPermission,
        fcmToken,
        isNotificationSupported,
        setUserId,
        setGroupId: setGroupIdWithStorage,
        setCohortId: setCohortIdWithStorage,
        setThread,
        setChannel,
        requestNotificationPermission,
        cleanup,
        refreshAuth,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
}
